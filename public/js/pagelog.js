(function () {
  var d = document;
  var g = d.createElement('script');
  var s = d.getElementsByTagName('script')[0];
  g.type = 'text/javascript';
  g.async = true;
  g.defer = true;
  g.src = 'js/skywalking.js';
  s.parentNode.insertBefore(g, s);
  if (!window.ClientMonitorTags) {
    window.ClientMonitorTags = [];
  }
  function pageLog() {
    try {
      ClientMonitor.register({
        collector: 'https://dcnmonitor.telecomjs.com:28305',
        // collector: 'http://localhost:28080',
        service: 'yuanfeng-web',
        serviceVersion: 'v1.0.0',
        useFmp: true,
        customTags: [ // 自定义上报程序
          {
            key: 'systemCode', //系统编码
            value: 'yuanfeng-001'
          }//,
		      // {
          //   key: 'env',
          //   value: 'prod'   // 测试 ：test / 生产 ：prod
          // },
          // {
          //   key: 'email',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // },
          // {
          //   key: 'chinese_name',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // }
          // ,
          // {
          //   key: 'mobile',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // }
          // ,
          // {
          //   key: 'lastLogin',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // }
          // ,
          // {
          //   key: 'username',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // }
          // ,
          // {
          //   key: 'userId',
          //   value: '0'   // 测试 ：test / 生产 ：prod
          // }

        ]
      });
    } catch (error) {}
  }

  if (window.ClientMonitor) {
    pageLog();
  } else {
    setTimeout(() => {
      if (window.ClientMonitor) {
        pageLog();
      }
    }, 2000);
  }
})();
