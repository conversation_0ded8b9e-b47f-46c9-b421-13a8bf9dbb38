FROM artifact.srdcloud.cn/public-docker-virtual/nginx


RUN echo "deb http://deb.debian.org/debian buster main" > /etc/apt/sources.list

RUN echo "deb http://security.debian.org/debian-security buster/updates main" >> /etc/apt/sources.list

RUN echo "deb http://deb.debian.org/debian buster-updates main" >> /etc/apt/sources.list

RUN echo "Acquire::Check-Valid-Until \"false\";" >> /etc/apt/apt.conf

RUN sed -i 's/http:\/\/deb.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/mirror-release-debian-tsinghua-remote/g' /etc/apt/sources.list


RUN sed -i 's/http:\/\/security.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/mirror-release-debian-tsinghua-remote/g' /etc/apt/sources.list

# RUN curl -fsSL https://artifact.srdcloud.cn/artifactory/docker-release-debian-remote/linux/debian/gpg |apt-key add -


RUN rm -rf /etc/apt/sources.list.d

# 更新APT缓存
RUN apt-get clean
RUN apt-get update
RUN apt-get install telnet -y
RUN apt-get install netcat -y
RUN apt-get install iputils-ping traceroute -y
RUN apt-get install wget -y
RUN apt-get install xz-utils -y

ENV HTTP_USER wuchangzhengjs
ENV HTTP_PASSWORD AKCp8mZcU9xYmCPcXiJcacq3fDkA26n9NQBL4C91YxVLSytCbAG7crCiRFRAv4MZqDSTodAvv


RUN wget https://artifact.srdcloud.cn/artifactory/xxcyw_2022-snapshot-generic-local/tools/node/node-v22.14.0-linux-x64.tar.xz --http-user=${HTTP_USER} --http-password=${HTTP_PASSWORD}

RUN tar -xJvf node-v22.14.0-linux-x64.tar.xz -C /usr/local --strip-components=1

RUN npm config set registry https://artifact.srdcloud.cn/artifactory/api/npm/public-npm-virtual/

RUN npm install -g wscat

MAINTAINER "oss_wuchangzheng"
RUN mkdir -p /app/
ADD dist.tar.gz /app/
ADD nginx.conf /etc/nginx/nginx.conf
WORKDIR /app/
ENV TZ=Asia/Shanghai
EXPOSE 80/tcp
CMD ["nginx","-g","daemon off;"]
